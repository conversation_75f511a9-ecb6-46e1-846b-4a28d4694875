import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { submitLRSDeclarationSagaAction } from "../../Store/SagaActions/DocumentSagaActions";
import "./LRSDeclaration.css";
import { IoArrowBackOutline } from "react-icons/io5";
import Button from "../../Components/Elements/Button";

const LRSDeclaration = ({ onComplete, vcipid, setShowLrsComponent }) => {
    const cssProperties = useSelector((state) => state.HomeReducer.cssProperties);
    const dispatch = useDispatch();
    const [formData, setFormData] = useState({
        hasTransaction: "",
        numTransactions: "",
    });
    const [transactions, setTransactions] = useState([]);
    const [errors, setErrors] = useState({});
    const currencies = ["USD", "EUR", "GBP", "JPY", "AUD", "CAD", "INR", "QAR"];
    const transactionOptions = Array.from({ length: 10 }, (_, i) => i + 1);

    useEffect(() => {
        if (formData.hasTransaction === "Yes" && formData.numTransactions) {
            setTransactions(
                Array(Number(formData.numTransactions)).fill().map((_, idx) => ({
                    transactionNum: (idx + 1).toString(),
                    transactionDate: "",
                    amount: "",
                    currency: "",
                    inramount: "",
                    bankname: ""
                }))
            );
        } else {
            setTransactions([]);
        }
    }, [formData.hasTransaction, formData.numTransactions]);

    const handleChange = e => {
        const { name, value } = e.target;
        setFormData(prevData => ({ ...prevData, [name]: value }));
    };

    const handleRadioChange = e => {
        const { value } = e.target;
        setFormData({ hasTransaction: value, numTransactions: "" });
        setTransactions([]);
        setErrors({});
        if (value === "No") {
            console.log("No selected");
        }
    };

    const handleTransactionChange = (idx, e) => {
        const { name, value } = e.target;
        setTransactions(prevTransactions => {
            const updatedTransactions = [...prevTransactions];
            updatedTransactions[idx][name] = value;

            // will Remove later
            if (name === 'currency' && value === 'INR') {
                updatedTransactions[idx]['inramount'] = updatedTransactions[idx]['amount'] || '';
            }
            else if (name === 'currency' && value !== 'INR') {
            }
            else if (name === 'amount' && updatedTransactions[idx]['currency'] === 'INR') {
                updatedTransactions[idx]['inramount'] = value;
            }

            return updatedTransactions;
        });
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.hasTransaction) {
            newErrors.hasTransaction = "Please select an option";
        }
        if (formData.hasTransaction === "Yes") {
            if (!formData.numTransactions) {
                newErrors.numTransactions = "Please select number of transactions";
            }
            transactions.forEach((txn, idx) => {
                if (!txn.transactionDate) newErrors[`transactionDate${idx}`] = "Please enter transaction date";
                if (!txn.amount) newErrors[`amount${idx}`] = "Please enter amount";
                if (!txn.currency) newErrors[`currency${idx}`] = "Please select currency";
                if (!txn.inramount) newErrors[`inramount${idx}`] = "Please enter INR amount";
                if (!txn.bankname) newErrors[`bankname${idx}`] = "Please enter bank name & address";
            });
        }
        setErrors(newErrors);
        return !Object.keys(newErrors).length;
    };

    const handleSubmit = () => {
        if (!validate()) return;

        // If "No" is selected, just complete without API call
        if (formData.hasTransaction === "No") {
            const body = {
                vcipid,
                numberOfTransactions: 0,
                transactionData: [],
                hasTransaction: "No"
            };
            onComplete && onComplete(body);
            return;
        }
        // If "Yes" is selected, proceed with API call
        const transactionData = transactions.map(txn => ({
            transactionNum: txn.transactionNum,
            transactionDate: txn.transactionDate.split("-").reverse().join("-"),
            amount: Number(txn.amount),
            currency: txn.currency,
            inramount: Number(txn.inramount),
            bankname: txn.bankname
        }));

        const body = {
            vcipid,
            numberOfTransactions: Number(formData.numTransactions),
            transactionData
        };

        dispatch(submitLRSDeclarationSagaAction({
            model: body,
            callback: (data) => {
                if (data.respcode === "200") {
                    onComplete && onComplete(body);
                } else {
                    console.error(data.respdesc || "Submission failed");
                }
            }
        }));
    };

    const handleBack = () => {
        setShowLrsComponent(false)
    };

    return (
        <div className="lrs-declaration-container">
            <div className="header">
                <div className="app-body-img" onClick={handleBack}>
                    <IoArrowBackOutline size={28} />
                </div>
                <div className="title-wrap">
                    <h1 className="declaration-title">LRS Declaration</h1>
                </div>
            </div>
            <p className="step-indicator">Step 2 of 2</p>
            <div className="declaration-form">
                <div>
                    <p className="declaration-text">
                        I declare that I have made a transaction under the Liberalised Remittance Scheme in the current financial year (April-March)*
                    </p>
                    <div className="radio-group">
                        <label>
                            <input
                                type="radio"
                                name="hasTransaction"
                                value="Yes"
                                checked={formData.hasTransaction === "Yes"}
                                onChange={handleRadioChange}
                            /> Yes
                        </label>
                        <label>
                            <input
                                type="radio"
                                name="hasTransaction"
                                value="No"
                                checked={formData.hasTransaction === "No"}
                                onChange={handleRadioChange}
                            /> No
                        </label>
                        {errors.hasTransaction && <span className="error-message">{errors.hasTransaction}</span>}
                    </div>
                    {formData.hasTransaction === "Yes" && (
                        <>
                            <div className="form-group">
                                <label>No. of Transactions *</label>
                                <select
                                    name="numTransactions"
                                    value={formData.numTransactions}
                                    onChange={handleChange}
                                    className="form-control"
                                >
                                    <option value="">Select</option>
                                    {transactionOptions.map(num => <option key={num} value={num}>{num}</option>)}
                                </select>
                                {errors.numTransactions && <span className="error-message">{errors.numTransactions}</span>}
                            </div>
                            {transactions.map((txn, idx) => (
                                <div key={idx} className="transaction-block">
                                    <h4>Transaction {idx + 1}</h4>
                                    {['transactionDate', 'amount', 'currency', 'inramount', 'bankname'].map(field => (
                                        <div className="form-group" key={field}>
                                            <label>
                                                {field === 'transactionDate'
                                                    ? 'Date of Transaction'
                                                    : field === 'inramount'
                                                        ? 'Amount in Rupees (₹)'
                                                        : field === 'bankname'
                                                            ? 'Bank Name & Address'
                                                            : field === 'currency'
                                                                ? 'Currency'
                                                                : 'Amount'} *
                                            </label>
                                            {field === 'currency' ? (
                                                <select
                                                    name={field}
                                                    value={txn[field]}
                                                    onChange={e => handleTransactionChange(idx, e)}
                                                    className="form-control"
                                                >
                                                    <option value="">Select</option>
                                                    {currencies.map(curr => <option key={curr} value={curr}>{curr}</option>)}
                                                    <option value="Other">Other</option>
                                                </select>
                                            ) : (
                                                <input
                                                    type={field === 'transactionDate' ? 'date' : 'text'}
                                                    name={field}
                                                    value={txn[field]}
                                                    onChange={e => handleTransactionChange(idx, e)}
                                                    className="form-control"
                                                    placeholder={field === 'inramount' && txn.currency === 'INR' ? 'Amount will be same as above' : ''}
                                                />
                                            )}
                                            {errors[`${field}${idx}`] && <span className="error-message">{errors[`${field}${idx}`]}</span>}
                                        </div>
                                    ))}
                                </div>
                            ))}
                        </>
                    )}
                </div>
            </div>
            <Button
                click={handleSubmit}
                title="Save"
                color={cssProperties?.button?.text_color}
                fontSize={cssProperties?.button?.font_size}
                backgroundColor={cssProperties?.button?.color}
            />
        </div>
    );
};

export default LRSDeclaration;