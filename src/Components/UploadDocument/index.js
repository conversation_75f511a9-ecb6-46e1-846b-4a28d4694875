import { useState } from "react";
import "./styles.css";
import { CiEdit, <PERSON>iLock, CiUnlock } from "react-icons/ci";
import { FaCheckCircle } from "react-icons/fa";
import UploadDocumentProcessCmp from "./UploadDocumentProcessCmp";
import LrsDeclarationCmp from "./LRSDeclaration";
import { actionGetCustomerConsent } from "../../Store/SagaActions/GetVcipDetailsSagaActions";
import { checkIfOccupationIsEmpty, isFeatureFlagEnabled } from "../../utils/validations";
import { useDispatch, useSelector } from "react-redux";
import CleintNames from "../../Constants/ClientNames";
import { useNavigate } from "react-router-dom";
import RouteNames from "../../Constants/RouteNames";
import Button from "../../Components/Elements/Button";

const UploadDocumentProcess = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [documentUploaded, setDocumentUploaded] = useState(false);
    const [lrsCompleted, setLrsCompleted] = useState(false);
    const stepsCompleted = (documentUploaded ? 1 : 0) + (lrsCompleted ? 1 : 0);
    const [showUploadComponent, setShowUploadComponent] = useState(false);
    const [showLrsComponent, setShowLrsComponent] = useState(false);
    const [uploadedDocuments, setUploadedDocuments] = useState({});
    const [lrsDeclarationData, setLrsDeclarationData] = useState({});
    const user = sessionStorage.getItem("user");
    const vcipid = JSON.parse(user)?.vcipid;
    const customerVcipDetails = JSON.parse(user)
    const clientName = useSelector((state) => state.HomeReducer.clientName);
    const dynamicSettingObj = useSelector((state) => state.HomeReducer.dynamicSettingObj);
    const cssProperties = useSelector((state) => state.HomeReducer.cssProperties);
    const vcipDetails = useSelector((state) => state.VcipReducer.vcipDetails);
    const { app_env, enableUserConsent, dynamicUserConsent, skipPanVerifyInCustomer, getBankDetails, applicationConfig } = dynamicSettingObj
    const showDynamicUserConsent = dynamicUserConsent === "true" ? true : false
    const { feature_flags } = vcipDetails
    const isConsentEnable = app_env?.includes("uat") ? "true" : enableUserConsent == "true"
    const isEnableUserConsent = isFeatureFlagEnabled("enable-user-consent", app_env, isConsentEnable)
    const skipPanScreens = skipPanVerifyInCustomer === "true" ? true : false
    const isEnableBankDetailsPage = isFeatureFlagEnabled("", "", getBankDetails);
    const isTCMergedScreen = applicationConfig?.isTCMergedScreen === "true" ? true : false
    const skipOccupationalScreen = applicationConfig?.skipOccupationalDetailsInCustomer === "true" ? true : false
    const isClientTideAccess =
        clientName === CleintNames?.TIDEDEV ||
            clientName === CleintNames?.TIDEQA ||
            clientName === CleintNames?.TIDE
            ? true
            : false;
    const isusfb = (clientName === CleintNames?.USFB) || (clientName === "USFBSYNTIZEN") ? true : false;
    const iskinabank = clientName === CleintNames?.KINABANK ? true : false;

    // Add this with other client checks
    const isThomascook = clientName === CleintNames?.THOMASCOOK || accName === CleintNames?.THOMASCOOK ? true : false;

    const navigateToUploadDocument = () => {
        setShowUploadComponent(true);
        setShowLrsComponent(false);
    };

    const navigateToLrsDeclaration = () => {
        if (documentUploaded) {
            setShowLrsComponent(true);
            setShowUploadComponent(false);
        }
    };

    const handleDocumentUploadComplete = (documentData) => {
        setUploadedDocuments(documentData);
        setDocumentUploaded(true);
        setShowUploadComponent(false);
    };

    const handleLrsDeclarationComplete = (data) => {
        setLrsDeclarationData(data);
        setLrsCompleted(true);
        setShowLrsComponent(false);
    };

    if (showUploadComponent) {
        return (
            <UploadDocumentProcessCmp
                setShowUploadComponent={setShowUploadComponent}
                vcipid={vcipid}
                onSave={handleDocumentUploadComplete}
            />
        );
    }

    if (showLrsComponent) {
        return (
            <LrsDeclarationCmp
                setShowLrsComponent={setShowLrsComponent}
                vcipid={vcipid}
                documents={uploadedDocuments}
                onComplete={handleLrsDeclarationComplete}
            />
        );
    }

    const startProcess1 = () => {
        const model = {
            vcipkey: sessionStorage.getItem("vcipkey"),
            custconsent: "I provide my consent to all the points mentioned here"
        }
        isEnableUserConsent && dispatch(actionGetCustomerConsent({ model: model }))
        if (customerVcipDetails?.kycstatus === "0") {
            if (customerVcipDetails?.panstatus === "1") {
                if (feature_flags?.kyctype === customeCodes?.EKYC || feature_flags?.kyctype === customeCodes?.COSMOS_EKYC) {
                    navigate(RouteNames.AADHAR_KYC_PROCESS_UPLOAD);
                } else {
                    navigate(RouteNames.AADHAR_KYC_KYC_DIGILOCCKER);
                }
            }
            else if (isTCMergedScreen) {
                navigate(RouteNames.AADHAR_KYC_KYC_DIGILOCCKER);
            }
            else {
                navigate(RouteNames.SELECT_KYC_PROCESS);
            }
        } else if (customerVcipDetails?.panstatus === "0" && !skipPanScreens) {
            if (isClientTideAccess) {
                navigate(RouteNames.PAN_CAPTURE, { replace: true });
            } else {
                if (iskinabank) {
                    navigate(RouteNames.KINA_SELECT_OVD_PROCESS, { replace: true });
                } else {
                    navigate(RouteNames.SELECT_PAN_KYC_PROCESS, { replace: true });
                }
            }
        } else if (customerVcipDetails?.videocallstatus === "0") {
            //need to add bank account empty condtion navigation 
            if (isEnableBankDetailsPage && (customerVcipDetails?.accverification ? customerVcipDetails?.accverification == "0" : true)) {
                navigate(RouteNames.BANK_DETAILS);
            }
            else if (checkIfOccupationIsEmpty(customerVcipDetails?.custoccupname) && !isusfb && !skipOccupationalScreen) {
                navigate(RouteNames.EMPLOYEMENT_DETAILS);
            } else {
                navigate(RouteNames.SELECT_VIDEOCALL_LANGAUAGE);
            }
        } else {
            // navigate(RouteNames.SELECT_VIDEOCALL_LANGAUAGE);
            if (checkIfOccupationIsEmpty(customerVcipDetails?.custoccupname) && !isusfb && !skipOccupationalScreen) {
                navigate(RouteNames.EMPLOYEMENT_DETAILS);
            } else {
                navigate(RouteNames.SELECT_VIDEOCALL_LANGAUAGE);
            }
        }
    };

    return (
        <div className="upload-document-container">
            <div className="company-header">
                <img src="/images/thomascook.png" alt="Thomas Cook" className="company-logo" />
            </div>

            <div className="progress-indicator">
                <span>{stepsCompleted} / 2 Steps Completed</span>
                <div className="progress-bars-container">
                    <div className="progress-step">
                        <div className="progress-bar">
                            <div
                                className="progress-filled"
                                style={{ width: documentUploaded ? '100%' : '0%' }}
                            ></div>
                        </div>
                    </div>
                    <div className="progress-step">
                        <div className="progress-bar">
                            <div
                                className="progress-filled"
                                style={{ width: lrsCompleted ? '100%' : '0%' }}
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="steps-container">
                <button
                    className={`step-button ${documentUploaded ? 'completed' : ''}`}
                    onClick={navigateToUploadDocument}
                >
                    <span className="step-text">Upload Documents *</span>
                    <span className="step-icon edit-icon">
                        {documentUploaded ? <FaCheckCircle color="green" /> : <CiEdit />}
                    </span>
                </button>

                <button
                    className={`step-button ${lrsCompleted ? 'completed' : ''}`}
                    disabled={!documentUploaded}
                    onClick={navigateToLrsDeclaration}
                >
                    <span className="step-text">LRS Declaration</span>
                    <span className="step-icon lock-icon">
                        {lrsCompleted ? <FaCheckCircle color="green" /> :
                            (documentUploaded ? <CiUnlock /> : <CiLock />)}
                    </span>
                </button>
            </div>

            <Button
                className="fixed-bottom-button"
                disabled={stepsCompleted < 2}
                onClick={startProcess1}
                title="Submit"
                style={{
                    width: '100%',
                    padding: '14px',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '500',
                    cursor: stepsCompleted < 2 ? 'not-allowed' : 'pointer',
                    transition: 'background-color 0.2s',
                    color: cssProperties?.button?.text_color,
                    fontSize: cssProperties?.button?.font_size,
                    backgroundColor: stepsCompleted < 2 ? '#a8bce0' : cssProperties?.button?.color
                }}
            />
        </div>
    );
};

export default UploadDocumentProcess;
