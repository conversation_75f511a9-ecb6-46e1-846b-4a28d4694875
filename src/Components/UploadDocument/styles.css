.upload-document-container {
    display: flex;
    flex-direction: column;
    min-height: 98vh;
    width: 100%;
    /* max-width: 400px; */
    margin: 0 auto;
    padding: 20px;
    /* background-color: #ffffff;
    border-radius: 12px; */
    /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
    font-family: Arial, sans-serif;
}

.fixed-bottom-button {
    margin-top: auto;
    width: 100%;
}

.company-header {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
}

.company-logo {
    height: 40px;
}

.progress-indicator {
    margin-bottom: 24px;
    color: #8e8e8e;
    font-size: 14px;
    text-align: right;
}

.progress-bars-container {
    display: flex;
    gap: 8px;
    width: 100%;
}

.progress-step {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.step-label {
    font-size: 12px;
    margin-bottom: 4px;
    color: #666;
}

.progress-bar {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-filled {
    height: 100%;
    background-color: #0066cc;
    transition: width 0.3s ease;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
}

.step-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 16px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 8px;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
}

.step-button:hover:not(:disabled) {
    background-color: #ebebeb;
}

.step-button.completed {
    background-color: #f0f8ff;
    border: 1px solid #0066cc;
}

.step-text {
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.step-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* .edit-icon {
    background-color: #ff6633;
    color: white;
} */

/* .lock-icon {
    background-color: #8e8e8e;
    color: white;
} */

.submit-button {
    width: 100%;
    padding: 14px;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
}

.submit-button:disabled {
    cursor: not-allowed;
}

.step-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Additional styles for the UploadDocumentProcessCmp component */

.upload-document-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.document-upload-section {
    margin: 20px 0;
}

.document-item {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.document-item h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

.file-info {
    margin-top: 8px;
    font-size: 14px;
    color: #0066cc;
}

/* .save-button {
    background-color: #0066cc;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.save-button:hover {
    background-color: #0055aa;
}

.save-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
} */