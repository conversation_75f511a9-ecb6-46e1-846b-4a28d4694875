import { useState, useRef, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import "./uploadDocumentProcess.css";
import { MdUploadFile, MdEdit } from "react-icons/md";
import toast from "react-hot-toast";
import {
  submitCustomerDocumentsSagaAction,
  getDocumentListSagaAction,
  fetchDocumentListDataSagaAction
} from "../../Store/SagaActions/DocumentSagaActions";
import { IoArrowBackOutline } from "react-icons/io5";
import DocumentSection from "./DocumentSection";
import UpdateDocumentPopup from "./UpdateDocumentPopup";
import Button from "../../Components/Elements/Button";

const UploadedDocumentSection = ({ title, docURL, datecreated, onUpdate }) => (
  <div className="document-section uploaded">
    <div className="document-type">{title}</div>
    <div className="uploaded-file">
      <div className="file-icon">
        <MdUploadFile size={24} />
      </div>
      <div className="file-details">
        <div className="file-name-size">
          <span className="file-name">{title}</span>
          {datecreated && (
            <span className="file-size" style={{ marginLeft: 8, color: "#888", fontSize: 12 }}>
              ({new Date(datecreated).toLocaleString()})
            </span>
          )}
        </div>
        <a
          className="view-button"
          href={docURL}
          target="_blank"
          rel="noopener noreferrer"
          style={{ textDecoration: "none" }}
        >
          View Document
        </a>
      </div>
      <button
        className="update-button"
        onClick={onUpdate}
        title="Update Document"
      >
        <MdEdit size={20} color="#007bff" />
      </button>
    </div>
  </div>
);

const formatFileSize = size => `${Math.round(size / 1024)} KiB`;

const UploadDocumentProcessCmp = ({ onSave, vcipid, setShowUploadComponent }) => {
  const cssProperties = useSelector((state) => state.HomeReducer.cssProperties);
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [documentConfig, setDocumentConfig] = useState({});
  const [documents, setDocuments] = useState({});
  const [fileObjects, setFileObjects] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fetchedDocuments, setFetchedDocuments] = useState([]);
  const [docNameToTitleMap, setDocNameToTitleMap] = useState({});
  const [documentsBeingUpdated, setDocumentsBeingUpdated] = useState({});
  const [showUpdateConfirmation, setShowUpdateConfirmation] = useState(false);
  const [documentToUpdate, setDocumentToUpdate] = useState(null);
  const inputRefsContainer = useRef({});

  // Document list response handlers
  const handlefetchDocumentListDataResponse = useCallback((
    fetchResp,
    config,
    setFetchedDocuments,
    setDocumentConfig,
    setDocuments,
    setFileObjects,
    setIsLoading
  ) => {
    let uploadedKeys = [];
    if (
      fetchResp &&
      fetchResp.respcode === "200" &&
      Array.isArray(fetchResp.documents)
    ) {
      setFetchedDocuments(fetchResp.documents);
      uploadedKeys = fetchResp.documents.map(
        doc => doc.docname?.toLowerCase().replace(/[\s_-]/g, "")
      );
    }
    const filteredConfig = {};
    Object.entries(config).forEach(([docKey, docCfg]) => {
      if (!uploadedKeys.includes(docCfg.key)) {
        filteredConfig[docKey] = docCfg;
      }
    });
    setDocumentConfig(filteredConfig);
    const docsState = {};
    const fileObjs = {};
    Object.keys(filteredConfig).forEach(key => {
      docsState[key] = { file: null, uploaded: false };
      fileObjs[key] = null;
    });
    setDocuments(docsState);
    setFileObjects(fileObjs);
    setIsLoading(false);
  }, []);

  const handleDocumentListResponse = useCallback(({
    response,
    vcipid,
    setDocNameToTitleMap,
    dispatch,
    setFetchedDocuments,
    setDocumentConfig,
    setDocuments,
    setFileObjects,
    setIsLoading
  }) => {
    if (
      response &&
      response.respcode === "200" &&
      Array.isArray(response.supportDocList)
    ) {
      const map = {};
      const config = {};
      response.supportDocList.forEach(doc => {
        const key = doc.documentname.toLowerCase().replace(/[\s_-]/g, "");
        map[key] = doc.documentname;
        const docKey = `doc_${doc.sdlid}`;
        config[docKey] = {
          title: doc.documentname,
          required: doc.status === 1,
          purposeid: doc.purposeid,
          sdlid: doc.sdlid,
          key,
        };
      });
      setDocNameToTitleMap(map);

      dispatch(
        fetchDocumentListDataSagaAction({
          model: { vcipid },
          callback: (fetchResp) => {
            handlefetchDocumentListDataResponse(
              fetchResp,
              config,
              setFetchedDocuments,
              setDocumentConfig,
              setDocuments,
              setFileObjects,
              setIsLoading
            );
          }
        })
      );
    } else {
      toast.error("Failed to load document list");
      setIsLoading(false);
    }
  }, [handlefetchDocumentListDataResponse]);

  useEffect(() => {
    if (!vcipid) {
      toast.error("Session key not found. Please login again.");
      setIsLoading(false);
      return;
    }
    dispatch(
      getDocumentListSagaAction({
        model: { vcipid },
        callback: (response) => {
          handleDocumentListResponse({
            response,
            vcipid,
            setDocNameToTitleMap,
            dispatch,
            setFetchedDocuments,
            setDocumentConfig,
            setDocuments,
            setFileObjects,
            setIsLoading
          });
        }
      })
    );
  }, [vcipid, dispatch, handleDocumentListResponse]);

    const handleFileSelect = useCallback((documentType, event) => {
        const file = event.target.files[0];
        if (file) {
            setDocuments(prev => ({
                ...prev,
                [documentType]: {
                    file: { name: file.name, size: formatFileSize(file.size) },
                    uploaded: true
                }
            }));

      setFileObjects(prev => ({
        ...prev,
        [documentType]: { file }
      }));

            const reader = new FileReader();
            reader.onloadend = () => {
                const base64String = reader.result.split(',')[1];
                setFileObjects(prev => ({
                    ...prev,
                    [documentType]: {
                        ...prev[documentType],
                        base64: base64String
                    }
                }));
            };
            reader.readAsDataURL(file);
        }
    }, []);

    const handleViewDocument = useCallback((documentType) => {
        const doc = documents[documentType];
        if (doc?.url) {
            window.open(doc.url, '_blank');
        } else {
            const fileObj = fileObjects[documentType];
            if (fileObj?.file) {
                const fileURL = URL.createObjectURL(fileObj.file);
                window.open(fileURL, '_blank');
            }
        }
    }, [documents, fileObjects]);

  const handleRemoveDocument = useCallback((documentType) => {
    setDocuments(prev => ({
      ...prev,
      [documentType]: { file: null, uploaded: false }
    }));
    setFileObjects(prev => ({
      ...prev,
      [documentType]: null
    }));
  }, []);
  
  const handleUpdateDocument = useCallback((docname) => {
    setDocumentToUpdate(docname);
    setShowUpdateConfirmation(true);
  }, []);

  const handleConfirmUpdate = useCallback(() => {
    if (documentToUpdate) {
      const updateKey = `update_${documentToUpdate.toLowerCase().replace(/[\s_-]/g, "")}`;
      
      setDocumentsBeingUpdated(prev => ({
        ...prev,
        [updateKey]: {
          originalDocName: documentToUpdate,
          title: documentToUpdate
        }
      }));

      setDocuments(prev => ({
        ...prev,
        [updateKey]: { file: null, uploaded: false }
      }));

      setFileObjects(prev => ({
        ...prev,
        [updateKey]: null
      }));

      setShowUpdateConfirmation(false);
      setDocumentToUpdate(null);

      setTimeout(() => {
        if (inputRefsContainer.current[updateKey]) {
          inputRefsContainer.current[updateKey].click();
        }
      }, 100);
    }
  }, [documentToUpdate]);

  const handleCancelUpdate = useCallback((updateKey) => {
    setDocumentsBeingUpdated(prev => {
      const newState = { ...prev };
      delete newState[updateKey];
      return newState;
    });

    setDocuments(prev => {
      const newState = { ...prev };
      delete newState[updateKey];
      return newState;
    });

    setFileObjects(prev => {
      const newState = { ...prev };
      delete newState[updateKey];
      return newState;
    });
  }, []);

  const handlePopupCancel = useCallback(() => {
    setShowUpdateConfirmation(false);
    setDocumentToUpdate(null);
  }, []);

  const handleDocumentUploadResponse = useCallback((response) => {
    if (response && response.respcode === "200") {
      const documentData = {};
      Object.keys(documents).forEach(key => {
        if (documents[key].uploaded) {
          documentData[key] = {
            name: documents[key].file.name,
            size: documents[key].file.size
          };
        }
      });
      toast.success("Documents uploaded successfully!");
      onSave(documentData);
    } else {
      const errorMessage = response?.respdesc || response?.message || "Failed to upload documents";
      toast.error(errorMessage);
      setIsSubmitting(false);
    }
  }, [documents, onSave]);

  const handleSave = useCallback(() => {
    setIsSubmitting(true);
    if (!vcipid) {
      toast.error("Session key not found. Please login again.");
      setIsSubmitting(false);
      return;
    }

    // Check if user has new documents to upload
    const hasNewDocuments = Object.keys(fileObjects).some(key => 
      fileObjects[key] && fileObjects[key].base64
    );

    // If no new documents but user has existing documents, allow them to proceed
    if (!hasNewDocuments && fetchedDocuments.length > 0) {
      toast.success("Proceeding with existing documents!");
      onSave({});
      return;
    }

    // If no new documents and no existing documents, show error
    if (!hasNewDocuments && fetchedDocuments.length === 0) {
      toast.error("Please upload at least one document to proceed.");
      setIsSubmitting(false);
      return;
    }

    // Process new documents for upload
    const documentsArray = Object.keys(fileObjects)
      .filter(key => fileObjects[key] && fileObjects[key].base64)
      .map(key => {
        const config = documentConfig[key];
        const updateInfo = documentsBeingUpdated[key];

        if (updateInfo) {
          return {
            doc: fileObjects[key].base64 || "",
            docname: updateInfo.originalDocName
          };
        }
        
        return {
          doc: fileObjects[key].base64 || "",
          docname: config?.title || documents[key]?.file?.name || ""
        };
      });
    dispatch(
      submitCustomerDocumentsSagaAction({
        model: {
          vcipid,
          documents: documentsArray,
          makerstatus: "0"
        },
        callback: handleDocumentUploadResponse
      })
    );
  }, [vcipid, fileObjects, documentConfig, documents, documentsBeingUpdated, dispatch, handleDocumentUploadResponse, fetchedDocuments, onSave]);

  const handleBack = useCallback(() => setShowUploadComponent(false), [setShowUploadComponent]);

  const hasDocumentsToSave = Object.keys(documentConfig).length > 0 || Object.keys(documentsBeingUpdated).length > 0 || fetchedDocuments.length > 0;

  if (isLoading) {
    return (
      <div className="upload-document-process">
        <h1 className="document-title">Upload Documents</h1>
        <p className="step-indicator">Step 1 of 2</p>
        <div className="loading-indicator">Loading document requirements...</div>
      </div>
    );
  }

  return (
    <div className="upload-document-process">
      <div className="header">
        <div className="app-body-img" onClick={handleBack}>
          <IoArrowBackOutline size={28} />
        </div>
        <div className="title-wrap">
          <h1 className="document-title">Upload Documents</h1>
        </div>
      </div>
      <p className="step-indicator">Step 1 of 2</p>
      <div className="document-sections-wrap">
        {fetchedDocuments.map((doc, idx) => {
          const key = doc.docname?.toLowerCase().replace(/[\s_-]/g, "");
          const title = docNameToTitleMap[key] || doc.docname;
          const updateKey = `update_${key}`;
          if (documentsBeingUpdated[updateKey]) {
            return (
              <DocumentSection
                key={`updating-${idx}`}
                documentType={updateKey}
                title={`Update ${title}`}
                document={documents[updateKey] || { file: null, uploaded: false }}
                inputRefsContainer={inputRefsContainer}
                onFileSelect={handleFileSelect}
                onViewDocument={handleViewDocument}
                onRemoveDocument={() => handleCancelUpdate(updateKey)}
              />
            );
          }
          return (
            <UploadedDocumentSection
              key={`uploaded-${idx}`}
              title={title}
              docURL={doc.docURL}
              datecreated={doc.datecreated}
              onUpdate={() => handleUpdateDocument(doc.docname)}
            />
          );
        })}
        {Object.keys(documentConfig).length > 0 ? (
          Object.keys(documentConfig).map(docType => (
            <DocumentSection
              key={docType}
              documentType={docType}
              title={documentConfig[docType].title}
              document={documents[docType] || { file: null, uploaded: false }}
              inputRefsContainer={inputRefsContainer}
              onFileSelect={handleFileSelect}
              onViewDocument={handleViewDocument}
              onRemoveDocument={handleRemoveDocument}
            />
          ))
        ) : (
          fetchedDocuments.length === 0 && (
            <div className="no-documents-message">No document requirements found.</div>
          )
        )}
      </div>
      <Button
        click={handleSave}
        type="button"
        title={isSubmitting ? "Uploading..." : "Save"}
        disabled={isSubmitting || !hasDocumentsToSave}
        color={cssProperties?.button?.text_color}
        fontSize={cssProperties?.button?.font_size}
        backgroundColor={(isSubmitting || !hasDocumentsToSave) ? '#a8bce0' : cssProperties?.button?.color}
      />
      <UpdateDocumentPopup
        isVisible={showUpdateConfirmation}
        documentName={documentToUpdate}
        onConfirm={handleConfirmUpdate}
        onCancel={handlePopupCancel}
      />
    </div>
  );
};

export default UploadDocumentProcessCmp;
